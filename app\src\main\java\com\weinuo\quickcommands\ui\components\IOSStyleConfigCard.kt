package com.weinuo.quickcommands.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.ui.theme.config.DividerConfig

/**
 * iOS快捷指令风格的配置卡片组件
 *
 * 特点：
 * - 卡片风格与iOS快捷指令应用一致
 * - 主题感知的分割线显示（天空蓝主题显示，海洋蓝主题隐藏）
 * - 统一的圆角和间距设计
 * - 支持点击交互
 *
 * @param title 卡片标题
 * @param description 卡片描述（可选）
 * @param onClick 点击回调
 * @param modifier 修饰符
 * @param showDivider 是否显示分割线（会根据主题自动调整）
 */
@Composable
fun IOSStyleConfigCard(
    title: String,
    description: String? = null,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 主题感知的分割线显示逻辑
    val shouldShowDivider = remember(showDivider, themeManager.getCurrentThemeId()) {
        when (themeManager.getCurrentThemeId()) {
            "sky_blue" -> showDivider // 天空蓝主题：根据参数决定
            "ocean_blue" -> false     // 海洋蓝主题：隐藏分割线
            else -> showDivider       // 其他主题：根据参数决定
        }
    }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null // 移除涟漪效果，保持iOS风格
                ) { onClick() },
            shape = RoundedCornerShape(cardStyle.defaultCornerRadius),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainerLow
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = cardStyle.defaultElevation
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = cardStyle.defaultHorizontalPadding,
                        vertical = cardStyle.settingsVerticalPadding
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧内容
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    // 标题
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }

                // 右侧内容：描述值和箭头
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 描述作为当前值显示在右侧
                    if (description != null) {
                        Text(
                            text = description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    // 向右箭头
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                        contentDescription = "进入",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }

        // 分割线（主题感知）
        if (shouldShowDivider) {
            Spacer(modifier = Modifier.height(1.dp))

            // 使用主题的分割线组件
            themeContext.componentFactory.createDivider()(
                DividerConfig(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.3f)
                )
            )
        }
    }
}

/**
 * iOS快捷指令风格的配置卡片组（多个卡片组合）
 *
 * 用于显示一组相关的配置项，自动处理分割线的显示逻辑
 *
 * @param cards 卡片配置列表
 * @param modifier 修饰符
 */
@Composable
fun IOSStyleConfigCardGroup(
    cards: List<IOSStyleConfigCardData>,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 使用单个Card包装所有项目，创建iOS风格的分组效果
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(cardStyle.defaultCornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = cardStyle.defaultElevation
        )
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            cards.forEachIndexed { index, cardData ->
                // 卡片内容行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果，保持iOS风格
                        ) { cardData.onClick() }
                        .padding(
                            horizontal = cardStyle.defaultHorizontalPadding,
                            vertical = cardStyle.settingsVerticalPadding
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左侧标题
                    Text(
                        text = cardData.title,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.weight(1f)
                    )

                    // 右侧内容：描述值和箭头
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 描述作为当前值显示在右侧
                        if (cardData.description != null) {
                            Text(
                                text = cardData.description,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }

                        // 向右箭头
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                            contentDescription = "进入",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }

                // 分割线（除了最后一项）
                if (index < cards.size - 1) {
                    // 主题感知的分割线显示逻辑
                    val shouldShowDivider = remember(themeManager.getCurrentThemeId()) {
                        when (themeManager.getCurrentThemeId()) {
                            "sky_blue" -> true  // 天空蓝主题：显示分割线
                            "ocean_blue" -> false // 海洋蓝主题：隐藏分割线
                            else -> true        // 其他主题：显示分割线
                        }
                    }

                    if (shouldShowDivider) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = cardStyle.defaultHorizontalPadding),
                                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.3f)
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * iOS风格配置卡片数据类
 */
data class IOSStyleConfigCardData(
    val title: String,
    val description: String? = null,
    val onClick: () -> Unit
)
